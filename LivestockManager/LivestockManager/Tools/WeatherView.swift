import SwiftUI
import WeatherKit
import CoreLocation

struct WeatherView: View {
    @Environment(\.dismiss) private var dismiss
    @StateObject private var viewModel = WeatherViewModel()
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    Button(action: { dismiss() }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.black)
                    }
                    
                    Spacer()
                    
                    Text(L.Tools.weather)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    Button(action: { viewModel.refreshWeather() }) {
                        Image(systemName: "arrow.clockwise")
                            .font(.system(size: 16, weight: .medium))
                            .foregroundColor(.black)
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(Color.white)
                
                ScrollView {
                    VStack(spacing: 24) {
                        if viewModel.isLoading {
                            // 加载状态
                            LoadingView()
                        } else if viewModel.errorMessage != nil {
                            // 错误状态
                            ErrorView(message: viewModel.errorMessage ?? L.Tools.Weather.errorUnknown,
                                     action: { viewModel.refreshWeather() })
                        } else if let currentWeather = viewModel.currentWeather {
                            // 当前天气
                            CurrentWeatherView(
                                location: viewModel.locationName,
                                weather: currentWeather
                            )
                            
                            // 每小时预报
                            if !viewModel.hourlyForecast.isEmpty {
                                HourlyForecastView(forecast: viewModel.hourlyForecast)
                            }
                            
                            // 未来几天预报
                            if !viewModel.dailyForecast.isEmpty {
                                DailyForecastView(forecast: viewModel.dailyForecast)
                            }
                            
                            // 农事建议
                            FarmingSuggestionsView(weather: currentWeather, forecast: viewModel.dailyForecast)

                            // Apple Weather 归属信息
                            AppleWeatherAttributionView()
                        } else {
                            // 无数据状态
                            NoDataView(action: { viewModel.refreshWeather() })
                        }

                        // 底部安全区域
                        Color.clear.frame(height: 60)
                    }
                    .padding(16)
                }
                .background(Color(hex: "#F8F8F8"))
            }
            .navigationViewStyle(.stack)
            .navigationBarHidden(true)
            .onAppear {
                viewModel.checkLocationPermission()
            }
            .alert(L.Tools.Weather.locationPermissionTitle, isPresented: $viewModel.showingLocationAlert) {
                Button(L.Tools.Weather.cancel) { }
                Button(L.Tools.Weather.goToSettings) { viewModel.openSettings() }
            } message: {
                Text(L.Tools.Weather.needsPermissionMessage)
            }
        }
    }
}

// 视图模型
class WeatherViewModel: NSObject, ObservableObject {
    @Published var currentWeather: CurrentWeather? = nil
    @Published var hourlyForecast: [HourWeather] = []
    @Published var dailyForecast: [DayWeather] = []
    @Published var isLoading = true
    @Published var errorMessage: String? = nil
    @Published var locationName = L.Tools.Weather.gettingLocation
    @Published var showingLocationAlert = false
    
    private let weatherService = WeatherService.shared
    private let locationManager = CLLocationManager()
    private var hasRequestedLocation = false
    
    override init() {
        super.init()
        setupLocationManager()
    }
    
    func setupLocationManager() {
        locationManager.delegate = self
        locationManager.desiredAccuracy = kCLLocationAccuracyKilometer // 低精度足够天气使用
    }
    
    func checkLocationPermission() {
        let status = locationManager.authorizationStatus
        
        switch status {
        case .notDetermined:
            locationManager.requestWhenInUseAuthorization()
            hasRequestedLocation = true
        case .authorizedWhenInUse, .authorizedAlways:
            if !hasRequestedLocation {
                locationManager.requestLocation()
                hasRequestedLocation = true
            }
        case .denied, .restricted:
            errorMessage = L.Tools.Weather.errorLocation
            isLoading = false
            showingLocationAlert = true
        @unknown default:
            errorMessage = L.Tools.Weather.errorUnknown
            isLoading = false
        }
    }
    
    func refreshWeather() {
        isLoading = true
        errorMessage = nil
        
        // 检查是否有最后位置
        if let location = locationManager.location {
            fetchWeather(for: location)
            reverseGeocode(location)
        } else {
            locationManager.requestLocation()
        }
    }
    
    private func fetchWeather(for location: CLLocation) {
        Task {
            do {
                // 获取当前天气
                let weather = try await weatherService.weather(for: location)
                
                // 获取每小时预报（接下来12小时）
                let hourlyForecasts = try await weatherService.weather(
                    for: location,
                    including: .hourly
                )
                
                // 获取未来10天预报
                let dailyForecasts = try await weatherService.weather(
                    for: location,
                    including: .daily
                )
                
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    
                    self.currentWeather = CurrentWeather(
                        temperature: weather.currentWeather.temperature.value,
                        condition: weather.currentWeather.condition.description,
                        symbolName: weather.currentWeather.symbolName,
                        humidity: weather.currentWeather.humidity,
                        windSpeed: weather.currentWeather.wind.speed.value,
                        windDirection: weather.currentWeather.wind.compassDirection.description,
                        uvIndex: weather.currentWeather.uvIndex.value,
                        visibility: weather.currentWeather.visibility.value,
                        precipitationIntensity: weather.currentWeather.precipitationIntensity.value
                    )
                    
                    // 处理小时预报
                    self.hourlyForecast = hourlyForecasts.forecast.prefix(12).map { forecast in
                        HourWeather(
                            date: forecast.date,
                            temperature: forecast.temperature.value,
                            symbolName: forecast.symbolName,
                            precipitationChance: forecast.precipitationChance
                        )
                    }
                    
                    // 处理日预报
                    self.dailyForecast = dailyForecasts.forecast.prefix(7).map { forecast in
                        DayWeather(
                            date: forecast.date,
                            highTemperature: forecast.highTemperature.value,
                            lowTemperature: forecast.lowTemperature.value,
                            symbolName: forecast.symbolName,
                            precipitationChance: forecast.precipitationChance
                        )
                    }
                    
                    self.isLoading = false
                }
            } catch {
                DispatchQueue.main.async { [weak self] in
                    guard let self = self else { return }
                    self.errorMessage = "\\(L.Tools.Weather.errorFetch): \\(error.localizedDescription)"
                    self.isLoading = false
                }
            }
        }
    }
    
    private func reverseGeocode(_ location: CLLocation) {
        let geocoder = CLGeocoder()
        geocoder.reverseGeocodeLocation(location) { [weak self] placemarks, error in
            guard let self = self else { return }
            
            if let error = error {
                print("反向地理编码错误: \(error.localizedDescription)")
                return
            }
            
            if let placemark = placemarks?.first {
                DispatchQueue.main.async {
                    var locationText = ""
                    
                    if let city = placemark.locality {
                        locationText = city
                    } else if let area = placemark.administrativeArea {
                        locationText = area
                    }
                    
                    if let district = placemark.subLocality, !locationText.isEmpty {
                        locationText += " · \(district)"
                    }
                    
                    self.locationName = locationText.isEmpty ? L.Tools.Weather.unknownLocation : locationText
                }
            }
        }
    }
    
    func openSettings() {
        if let url = URL(string: UIApplication.openSettingsURLString) {
            UIApplication.shared.open(url)
        }
    }
}

// CLLocationManagerDelegate 扩展
extension WeatherViewModel: CLLocationManagerDelegate {
    func locationManager(_ manager: CLLocationManager, didUpdateLocations locations: [CLLocation]) {
        if let location = locations.first {
            fetchWeather(for: location)
            reverseGeocode(location)
            // 获取位置后停止更新，节省电量
            // manager.stopUpdatingLocation() // 如果只需要一次性位置，可以打开这个
        } else {
            errorMessage = L.Tools.Weather.errorLocationUnavailable
            isLoading = false
        }
    }
    
    func locationManager(_ manager: CLLocationManager, didFailWithError error: Error) {
        errorMessage = "\\(L.Tools.Weather.errorManagerFailed): \\(error.localizedDescription)"
        isLoading = false
        
        // 如果是权限问题，提示用户
        if let clError = error as? CLError, clError.code == .denied {
            showingLocationAlert = true
        }
    }
    
    func locationManagerDidChangeAuthorization(_ manager: CLLocationManager) {
        checkLocationPermission()
    }
}

// 天气数据模型
struct CurrentWeather: Identifiable {
    let id = UUID()
    let temperature: Double
    let condition: String
    let symbolName: String
    let humidity: Double
    let windSpeed: Double
    let windDirection: String
    let uvIndex: Int
    let visibility: Double // 可见度，单位米
    let precipitationIntensity: Double // 降水强度 mm/hr
    
    var formattedTemperature: String {
        "\(Int(temperature.rounded()))°C"
    }
    
    var formattedHumidity: String {
        "\(Int(humidity * 100))%"
    }
    
    var formattedWind: String {
        "\(Int(windSpeed.rounded())) km/h \(windDirection)"
    }
    
    var formattedVisibility: String {
        "\(Int((visibility/1000).rounded())) km"
    }
    
    var formattedUVIndex: String {
        "\(uvIndex) (\(uvDescription(uvIndex)))"
    }
    
    var formattedPrecipitation: String {
        "\(String(format: "%.1f", precipitationIntensity)) mm/h"
    }
    
    private func uvDescription(_ uvIndex: Int) -> String {
        switch uvIndex {
        case 0...2: return L.Tools.Weather.UV.low
        case 3...5: return L.Tools.Weather.UV.moderate
        case 6...7: return L.Tools.Weather.UV.high
        case 8...10: return L.Tools.Weather.UV.veryHigh
        default: return L.Tools.Weather.UV.extreme
        }
    }
}

struct HourWeather: Identifiable {
    let id = UUID()
    let date: Date
    let temperature: Double
    let symbolName: String
    let precipitationChance: Double // 降水概率 0.0-1.0
    
    var hourString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "HH"
        return formatter.string(from: date)
    }
    
    var formattedTemperature: String {
        "\(Int(temperature.rounded()))°"
    }
    
    var formattedPrecipitationChance: String {
        "\(Int(precipitationChance * 100))%"
    }
}

struct DayWeather: Identifiable {
    let id = UUID()
    let date: Date
    let highTemperature: Double
    let lowTemperature: Double
    let symbolName: String
    let precipitationChance: Double
    
    var dayString: String {
        let formatter = DateFormatter()
        formatter.dateFormat = "E"
        return formatter.string(from: date)
    }
    
    var formattedHighTemperature: String {
        "\(Int(highTemperature.rounded()))°"
    }
    
    var formattedLowTemperature: String {
        "\(Int(lowTemperature.rounded()))°"
    }
    
    var formattedPrecipitationChance: String {
        "\(Int(precipitationChance * 100))%"
    }
}

// MARK: - Subviews
struct LoadingView: View {
    var body: some View {
        VStack {
            ProgressView()
                .progressViewStyle(CircularProgressViewStyle(tint: .black))
                .scaleEffect(1.5)
                .padding(.bottom, 16)
            Text(L.Tools.Weather.loading)
                .font(.system(size: 16))
                .foregroundColor(Color.black.opacity(0.7))
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

struct ErrorView: View {
    let message: String
    let action: () -> Void
    
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "exclamationmark.triangle.fill")
                .font(.system(size: 40))
                .foregroundColor(.red)
            Text(message)
                .font(.system(size: 16))
                .foregroundColor(Color.black.opacity(0.8))
                .multilineTextAlignment(.center)
            Button(L.retry, action: action)
                .font(.system(size: 16, weight: .semibold))
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.black)
                .foregroundColor(.white)
                .cornerRadius(10)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

struct NoDataView: View {
    let action: () -> Void
    var body: some View {
        VStack(spacing: 20) {
            Image(systemName: "cloud.sun.fill")
                .font(.system(size: 40))
                .foregroundColor(Color.black.opacity(0.6))
            Text(L.Tools.Weather.noData)
                .font(.system(size: 16))
                .foregroundColor(Color.black.opacity(0.8))
                .multilineTextAlignment(.center)
            Button(L.refresh, action: action)
                .font(.system(size: 16, weight: .semibold))
                .padding(.horizontal, 24)
                .padding(.vertical, 12)
                .background(Color.black)
                .foregroundColor(.white)
                .cornerRadius(10)
        }
        .frame(maxWidth: .infinity, maxHeight: .infinity)
        .padding()
    }
}

struct CurrentWeatherView: View {
    let location: String
    let weather: CurrentWeather
    
    var body: some View {
        VStack(spacing: 16) {
            // 位置和当前温度
            HStack(alignment: .firstTextBaseline) {
                VStack(alignment: .leading, spacing: 4) {
                    Text(location)
                        .font(.system(size: 22, weight: .semibold))
                        .foregroundColor(.black)
                        .lineLimit(1)
                    Text(weather.condition)
                        .font(.system(size: 16))
                        .foregroundColor(Color.black.opacity(0.7))
                }
                Spacer()
                Image(systemName: weather.symbolName)
                    .font(.system(size: 48))
                    .foregroundColor(.black)
            }
            
            Text(weather.formattedTemperature)
                .font(.system(size: 80, weight: .semibold))
                .foregroundColor(.black)
                .frame(height: 80)
            
            // 天气详情网格
            LazyVGrid(columns: [GridItem(.flexible()), GridItem(.flexible())], spacing: 16) {
                WeatherDetailItem(label: L.Tools.Weather.humidity, value: weather.formattedHumidity, icon: "humidity.fill")
                WeatherDetailItem(label: L.Tools.Weather.wind, value: weather.formattedWind, icon: "wind")
                WeatherDetailItem(label: L.Tools.Weather.uvIndex, value: weather.formattedUVIndex, icon: "sun.max.fill")
                WeatherDetailItem(label: L.Tools.Weather.visibility, value: weather.formattedVisibility, icon: "eye.fill")
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(color: Color.black.opacity(0.05), radius: 10, y: 5)
    }
}

struct WeatherDetailItem: View {
    let label: String
    let value: String
    let icon: String
    
    var body: some View {
        HStack(spacing: 10) {
            Image(systemName: icon)
                .font(.system(size: 18))
                .foregroundColor(Color.black.opacity(0.7))
                .frame(width: 24)
            VStack(alignment: .leading, spacing: 2) {
                Text(label)
                    .font(.system(size: 13))
                    .foregroundColor(Color.black.opacity(0.6))
                Text(value)
                    .font(.system(size: 15, weight: .medium))
                    .foregroundColor(.black)
            }
            Spacer()
        }
        .padding(12)
        .background(Color(hex: "#F9F9F9"))
        .cornerRadius(12)
    }
}

struct HourlyForecastView: View {
    let forecast: [HourWeather]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(L.Tools.Weather.hourlyForecast)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.black)
            
            ScrollView(.horizontal, showsIndicators: false) {
                HStack(spacing: 16) {
                    ForEach(forecast) { hourWeather in
                        HourlyForecastItem(weather: hourWeather)
                    }
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(color: Color.black.opacity(0.05), radius: 10, y: 5)
    }
}

struct HourlyForecastItem: View {
    let weather: HourWeather
    
    var body: some View {
        VStack(spacing: 8) {
            Text(weather.hourString)
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(Color.black.opacity(0.8))
            Image(systemName: weather.symbolName)
                .font(.system(size: 22))
                .foregroundColor(.black)
                .frame(height: 25)
            Text(weather.formattedTemperature)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(.black)
            if weather.precipitationChance > 0.1 { // 只显示大于10%的降水概率
                HStack(spacing: 2) {
                    Image(systemName: "drop.fill")
                        .font(.caption)
                        .foregroundColor(Color.blue.opacity(0.7))
                    Text(weather.formattedPrecipitationChance)
                        .font(.caption)
                        .foregroundColor(Color.blue.opacity(0.9))
                }
            }
        }
        .padding(.vertical, 12)
        .padding(.horizontal, 8)
        .frame(width: 65)
        .background(Color(hex: "#F9F9F9"))
        .cornerRadius(12)
    }
}

struct DailyForecastView: View {
    let forecast: [DayWeather]
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(L.Tools.Weather.dailyForecast)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.black)
            
            VStack(spacing: 12) {
                ForEach(forecast) { dayWeather in
                    DailyForecastItem(weather: dayWeather)
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(color: Color.black.opacity(0.05), radius: 10, y: 5)
    }
}

struct DailyForecastItem: View {
    let weather: DayWeather
    
    var body: some View {
        HStack(spacing: 12) {
            Text(weather.dayString)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(Color.black.opacity(0.8))
                .frame(width: 40, alignment: .leading)
            
            Image(systemName: weather.symbolName)
                .font(.system(size: 20))
                .foregroundColor(.black)
                .frame(width: 25)
            
            if weather.precipitationChance > 0.1 {
                HStack(spacing: 2) {
                    Image(systemName: "drop.fill")
                        .font(.caption)
                        .foregroundColor(Color.blue.opacity(0.7))
                    Text(weather.formattedPrecipitationChance)
                        .font(.caption)
                        .foregroundColor(Color.blue.opacity(0.9))
                }
                .frame(width: 45, alignment: .leading)
            } else {
                Spacer().frame(width: 45)
            }
            
            Spacer()
            
            Text(weather.formattedLowTemperature)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(Color.black.opacity(0.7))
            
            // 温度条
            TemperatureBarView(
                lowTemp: weather.lowTemperature,
                highTemp: weather.highTemperature,
                overallMin: forecastOverallMinTemp(),
                overallMax: forecastOverallMaxTemp()
            )
            .frame(width: 80, height: 5)
            
            Text(weather.formattedHighTemperature)
                .font(.system(size: 15, weight: .medium))
                .foregroundColor(.black)
                .frame(width: 30, alignment: .trailing)
        }
        .padding(.vertical, 8)
    }
    
    // Helper to get overall min/max temps for scaling the bar
    // This assumes the parent view (DailyForecastView) passes the full forecast
    // Or it could be passed as a parameter if this item is more generic.
    private func forecastOverallMinTemp() -> Double {
        // Placeholder - in a real app, get this from the viewModel or pass it down
        // For now, just use a reasonable range or calculate if `forecast` is available here.
        return 0 // Example
    }
    
    private func forecastOverallMaxTemp() -> Double {
        return 35 // Example
    }
}

struct TemperatureBarView: View {
    let lowTemp: Double
    let highTemp: Double
    let overallMin: Double
    let overallMax: Double
    
    var body: some View {
        GeometryReader { geometry in
            let totalRange = max(1, overallMax - overallMin) // Avoid division by zero
            let barStart = CGFloat((lowTemp - overallMin) / totalRange) * geometry.size.width
            let barWidth = CGFloat((highTemp - lowTemp) / totalRange) * geometry.size.width
            
            ZStack(alignment: .leading) {
                Capsule()
                    .fill(Color.gray.opacity(0.2))
                    .frame(width: geometry.size.width, height: geometry.size.height)
                
                Capsule()
                    .fill(LinearGradient(
                        gradient: Gradient(colors: [.blue, .orange]),
                        startPoint: .leading,
                        endPoint: .trailing
                    ))
                    .frame(width: max(0, barWidth), height: geometry.size.height)
                    .offset(x: max(0, barStart))
            }
        }
    }
}

// 农事建议视图
struct FarmingSuggestionsView: View {
    let weather: CurrentWeather
    let forecast: [DayWeather] // 未来几天的预报

    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(L.Tools.Weather.farmingSuggestionTitle)
                .font(.system(size: 18, weight: .semibold))
                .foregroundColor(.black)

            VStack(alignment: .leading, spacing: 8) {
                ForEach(getSuggestions(), id: \.self) { suggestion in
                    HStack(alignment: .top, spacing: 8) {
                        Image(systemName: suggestion.icon)
                            .font(.system(size: 16))
                            .foregroundColor(suggestion.iconColor)
                            .frame(width: 20)
                        Text(suggestion.text)
                            .font(.system(size: 14))
                            .foregroundColor(Color.black.opacity(0.8))
                    }
                }
                if getSuggestions().isEmpty {
                    Text(L.Tools.Weather.noSpecificSuggestionMessage)
                        .font(.system(size: 14))
                        .foregroundColor(Color.black.opacity(0.6))
                }
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(20)
        .shadow(color: Color.black.opacity(0.05), radius: 10, y: 5)
    }

    private func getSuggestions() -> [Suggestion] {
        var suggestions: [Suggestion] = []
        let today = Calendar.current.startOfDay(for: Date())

        // 今日天气建议
        if weather.uvIndex >= 8 {
            suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.uvHigh, icon: "sun.max.fill", iconColor: .orange))
        }
        if weather.precipitationIntensity > 5.0 { // 大雨
            suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.heavyRainToday, icon: "cloud.heavyrain.fill", iconColor: .blue))
        }
        if weather.windSpeed > 30 { // 风速大于 30km/h (约等于 8.3m/s，5级风以上)
            suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.strongWindToday, icon: "wind", iconColor: .gray))
        }
        
        // 未来几天预报建议 (检查未来3天)
        let upcomingForecast = forecast.filter { $0.date >= today }.prefix(3)
        
        var willRainInNext3Days = false
        var highTempSpell = 0
        var lowTempSpell = 0
        
        for dayWeather in upcomingForecast {
            if dayWeather.precipitationChance > 0.6 {
                willRainInNext3Days = true
            }
            if dayWeather.highTemperature > 30 { // 高于30度
                highTempSpell += 1
            }
            if dayWeather.lowTemperature < 5 { // 低于5度
                lowTempSpell += 1
            }
        }
        
        if willRainInNext3Days {
            suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.rainNext3Days, icon: "cloud.rain.fill", iconColor: .blue))
        }
        
        if highTempSpell >= 2 {
            suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.heatWave, icon: "thermometer.sun.fill", iconColor: .red))
        }
        
        if lowTempSpell >= 2 {
            suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.coldSpell, icon: "thermometer.snowflake", iconColor: .cyan))
        }
        
        // 通用灌溉建议 (简化版)
        let daysSinceLastRain = 3 // 假设数据
        if daysSinceLastRain >= 3 && !willRainInNext3Days && weather.temperature > 20 {
             suggestions.append(Suggestion(text: L.Tools.Weather.Suggestion.irrigation, icon: "drop.fill", iconColor: .green))
        }
        
        return suggestions
    }
    
    struct Suggestion: Hashable {
        let text: String
        let icon: String
        let iconColor: Color
    }
}

// Apple Weather 归属视图
struct AppleWeatherAttributionView: View {
    var body: some View {
        HStack(spacing: 4) {
            Text("Powered by ")
            // 1. Apple Logo + "Weather" Text
            Image(systemName: "apple.logo")
            Text("Weather")

            // Spacer to separate the trademark from the link
            Spacer()

            // 2. Legal attribution link
            if let url = URL(string: "https://weatherkit.apple.com/legal-attribution.html") {
                Link(L.Tools.Weather.legalAttribution, destination: url)
            }
        }
        .font(.footnote)
        .foregroundColor(.secondary)
        .padding(.horizontal)
    }
}

// Preview
struct WeatherView_Previews: PreviewProvider {
    static var previews: some View {
        WeatherView()
    }
}