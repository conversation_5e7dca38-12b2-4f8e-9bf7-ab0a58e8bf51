import SwiftUI

struct CounterToolView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var count = 0
    @State private var countHistory: [CountRecord] = []
    @State private var showingRecordSheet = false
    @State private var showingHistory = false
    @State private var recordName = ""
    @State private var recordNote = ""
    @State private var isCountingActive = false
    @State private var currentSessionStartTime: Date?
    
    var body: some View {
        NavigationView {
            VStack(spacing: 0) {
                // 顶部导航栏
                HStack {
                    Button(action: { dismiss() }) {
                        Image(systemName: "chevron.left")
                            .font(.system(size: 16, weight: .semibold))
                            .foregroundColor(.black)
                    }
                    
                    Spacer()
                    
                    Text(L.Tools.counter)
                        .font(.system(size: 18, weight: .semibold))
                        .foregroundColor(.black)
                    
                    Spacer()
                    
                    Button(action: { showingHistory = true }) {
                        Image(systemName: "clock.arrow.circlepath")
                            .font(.system(size: 18))
                            .foregroundColor(Color.black.opacity(0.7))
                    }
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 16)
                .background(Color.white)
                
                ScrollView {
                    VStack(spacing: 24) {
                        // 计数器状态
                        VStack(spacing: 8) {
                            if isCountingActive {
                                // 当前记录信息
                                Text(recordName.isEmpty ? L.Tools.Counter.counting : recordName)
                                    .font(.system(size: 20, weight: .semibold))
                                    .foregroundColor(.black)
                                
                                if let startTime = currentSessionStartTime {
                                    Text(L.Tools.Counter.startTime.localized(with: formatDateTime(startTime)))
                                        .font(.system(size: 14))
                                        .foregroundColor(Color.black.opacity(0.6))
                                }
                            } else {
                                Text(L.Tools.Counter.newCount)
                                    .font(.system(size: 20, weight: .semibold))
                                    .foregroundColor(.black)
                            }
                        }
                        .padding(.top, 16)
                        
                        // 计数器显示
                        VStack(spacing: 24) {
                            // 当前计数
                            Text("\(count)")
                                .font(.system(size: 100, weight: .semibold))
                                .foregroundColor(.black)
                                .frame(height: 120)
                            
                            // 计数按钮
                            HStack(spacing: 20) {
                                // 减少按钮
                                Button(action: { decrementCount() }) {
                                    Image(systemName: "minus")
                                        .font(.system(size: 30, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 75, height: 75)
                                        .background(Color.black.opacity(0.8))
                                        .clipShape(Circle())
                                }
                                
                                // 增加按钮
                                Button(action: { incrementCount() }) {
                                    Image(systemName: "plus")
                                        .font(.system(size: 30, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 90, height: 90)
                                        .background(Color.black)
                                        .clipShape(Circle())
                                }
                                
                                // 多加按钮
                                Button(action: { incrementByFive() }) {
                                    Text("+5")
                                        .font(.system(size: 24, weight: .semibold))
                                        .foregroundColor(.white)
                                        .frame(width: 75, height: 75)
                                        .background(Color.black.opacity(0.8))
                                        .clipShape(Circle())
                                }
                            }
                            .padding(.bottom, 16)
                        }
                        .padding(24)
                        .background(Color.white)
                        .cornerRadius(20)
                        .overlay(
                            RoundedRectangle(cornerRadius: 20)
                                .stroke(Color.black.opacity(0.08), lineWidth: 1)
                        )
                        
                        // 底部按钮
                        HStack(spacing: 16) {
                            if isCountingActive {
                                // 完成按钮
                                Button(action: { showingRecordSheet = true }) {
                                    HStack {
                                        Text(L.Tools.Counter.complete)
                                            .font(.system(size: 16, weight: .semibold))
                                            .foregroundColor(.white)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 16)
                                    .background(Color.black)
                                    .cornerRadius(12)
                                }
                                
                                // 重置按钮
                                Button(action: { resetCounter() }) {
                                    HStack {
                                        Text(L.Tools.Counter.reset)
                                            .font(.system(size: 16, weight: .semibold))
                                            .foregroundColor(Color.black)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 16)
                                    .background(Color.white)
                                    .cornerRadius(12)
                                    .overlay(
                                        RoundedRectangle(cornerRadius: 12)
                                            .stroke(Color.black.opacity(0.08), lineWidth: 1)
                                    )
                                }
                            } else {
                                // 开始按钮
                                Button(action: { startNewCounting() }) {
                                    HStack {
                                        Text(L.Tools.Counter.start)
                                            .font(.system(size: 16, weight: .semibold))
                                            .foregroundColor(.white)
                                    }
                                    .frame(maxWidth: .infinity)
                                    .padding(.vertical, 16)
                                    .background(Color.black)
                                    .cornerRadius(12)
                                }
                            }
                        }
                        .padding(.top, 16)
                        
                        // 底部安全区域
                        Color.clear.frame(height: 40)
                    }
                    .padding(16)
                }
                .background(Color(hex: "#F8F8F8"))
            }
            .navigationViewStyle(.stack)
            .navigationBarHidden(true)
            .sheet(isPresented: $showingRecordSheet) {
                SaveCountRecordView(
                    count: count,
                    name: $recordName,
                    note: $recordNote,
                    onSave: {
                        saveCurrentRecord()
                        resetCountingSession()
                    },
                    onCancel: {
                        // 继续计数，不保存
                    }
                )
            }
            .sheet(isPresented: $showingHistory) {
                CountHistoryView(records: $countHistory)
            }
        }
    }
    
    // 开始新的计数会话
    private func startNewCounting() {
        isCountingActive = true
        currentSessionStartTime = Date()
        count = 0
        recordName = ""
        recordNote = ""
    }
    
    // 递增计数
    private func incrementCount() {
        if !isCountingActive {
            startNewCounting()
        }
        count += 1
    }
    
    // 递增5个
    private func incrementByFive() {
        if !isCountingActive {
            startNewCounting()
        }
        count += 5
    }
    
    // 递减计数
    private func decrementCount() {
        if !isCountingActive {
            return
        }
        count = max(0, count - 1)
    }
    
    // 重置计数器
    private func resetCounter() {
        count = 0
    }
    
    // 保存当前记录
    private func saveCurrentRecord() {
        guard isCountingActive, let startTime = currentSessionStartTime else { return }
        
        let record = CountRecord(
            id: UUID(),
            name: recordName.isEmpty ? "计数记录" : recordName,
            count: count,
            startTime: startTime,
            endTime: Date(),
            note: recordNote.isEmpty ? nil : recordNote
        )
        
        countHistory.append(record)
    }
    
    // 重置计数会话
    private func resetCountingSession() {
        isCountingActive = false
        currentSessionStartTime = nil
    }
    
    // 其他私有函数... (如果文件很长，这里可能只是部分内容)


    // MARK: - Helper Methods
    private func formatDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "yyyy-MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

struct CountRecord: Identifiable, Codable {
    let id: UUID
    var name: String
    var count: Int
    var startTime: Date
    var endTime: Date
    var note: String?
    var duration: TimeInterval {
        endTime.timeIntervalSince(startTime)
    }
}

struct SaveCountRecordView: View {
    let count: Int
    @Binding var name: String
    @Binding var note: String
    var onSave: () -> Void
    var onCancel: () -> Void
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            Form {
                Section(header: Text(L.Tools.Counter.Record.details).font(.system(size:16))) {
                    HStack {
                         Text(L.Tools.Counter.Record.count)
                             .font(.system(size: 16))
                         Spacer()
                         Text("\(count)")
                             .font(.system(size: 16))
                     }
                    TextField(L.Tools.Counter.Record.namePlaceholder, text: $name)
                         .font(.system(size: 16))
                    TextField(L.Tools.Counter.Record.notePlaceholder, text: $note, axis: .vertical)
                        .lineLimit(3...)
                        .font(.system(size: 16))
                }
            }
            .navigationTitle(L.Tools.Counter.Record.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarLeading) {
                    Button(L.cancel) {
                        onCancel()
                        dismiss()
                    }
                    .font(.system(size: 16))
                }
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(L.save) {
                        onSave()
                        dismiss()
                    }
                    .font(.system(size: 16))
                }
            }
        }
        .navigationViewStyle(.stack)
    }
}

struct CountHistoryView: View {
    @Binding var records: [CountRecord]
    @Environment(\.dismiss) private var dismiss

    var body: some View {
        NavigationView {
            List {
                if records.isEmpty {
                    Text(L.Tools.Counter.History.empty)
                        .foregroundColor(.gray)
                        .font(.system(size: 16))
                } else {
                    ForEach(records) { record in
                        VStack(alignment: .leading, spacing: 8) {
                            HStack {
                                Text(record.name)
                                    .font(.system(size: 17, weight: .semibold))
                                Spacer()
                                Text(L.Tools.Counter.History.countLabel.localized(with: record.count))
                                    .font(.system(size: 15))
                            }
                            
                            Text(L.Tools.Counter.History.timeLabel.localized(with: formatHistoryDateTime(record.startTime), formatHistoryDateTime(record.endTime)))
                                .font(.system(size: 13))
                                .foregroundColor(.gray)

                            if let note = record.note, !note.isEmpty {
                                Text(L.Tools.Counter.History.noteLabel.localized(with: note))
                                    .font(.system(size: 13))
                                    .foregroundColor(.gray)
                                    .padding(.top, 2)
                            }
                        }
                        .padding(.vertical, 8)
                    }
                    .onDelete(perform: deleteRecord)
                }
            }
            .navigationTitle(L.Tools.Counter.History.title)
            .navigationBarTitleDisplayMode(.inline)
            .toolbar {
                ToolbarItem(placement: .navigationBarTrailing) {
                    Button(L.done) {
                        dismiss()
                    }
                    .font(.system(size: 16))
                }
                ToolbarItem(placement: .navigationBarLeading) {
                     if !records.isEmpty {
                         EditButton()
                             .font(.system(size: 16))
                     }
                 }
            }
        }
        .navigationViewStyle(.stack)
    }

    private func deleteRecord(at offsets: IndexSet) {
        records.remove(atOffsets: offsets)
    }
    
    private func formatHistoryDateTime(_ date: Date) -> String {
        let formatter = DateFormatter()
        formatter.dateFormat = "MM-dd HH:mm"
        return formatter.string(from: date)
    }
}

// Preview
struct CounterToolView_Previews: PreviewProvider {
    static var previews: some View {
        CounterToolView()
    }
}

// L is a placeholder for localization strings
// For example:
// struct L {
//     struct Tools {
//         static let counter = "计数器"
//         struct Counter {
//             static let counting = "正在计数..."
//             static let startTime = "开始时间: %@"
//             static let newCount = "开始新的计数"
//             static let complete = "完成"
//             static let reset = "重置"
//             static let start = "开始计数"
//             struct Record {
//                 static let details = "记录详情"
//                 static let count = "计数值:"
//                 static let namePlaceholder = "记录名称 (可选)"
//                 static let notePlaceholder = "备注 (可选)"
//                 static let title = "保存记录"
//             }
//             struct History {
//                 static let empty = "暂无历史记录"
//                 static let countLabel = "数量: %lld"
//                 static let timeLabel = "时间: %@ - %@"
//                 static let noteLabel = "备注: %@"
//                 static let title = "历史记录"
//             }
//         }
//     }
//     struct Actions {
//         static let cancel = "取消"
//         static let save = "保存"
//         static let done = "完成"
//     }
// } 