import SwiftUI

struct AnimalDetailView: View {
    let livestock: Animal
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var dataManager: AnimalDataManager
    @State private var showingEditSheet = false
    @State private var showingDeleteAlert = false
    @State private var showingFullImage = false
    
    var body: some View {
        ScrollView {
            VStack(spacing: 16) {
                // 基本信息卡片
                BasicInfoCard(livestock: livestock, onImageTap: {
                    showingFullImage = true
                })
                .padding(.horizontal, 16)
                .padding(.top, 16)
                
                // 详细信息部分
                VStack(spacing: 16) {
                    // 基础信息
                    DetailSection(title: L.Detail.basicInfo) {
                        DetailRow(title: L.Animal.id, value: livestock.visualId)
                        DetailRow(title: L.Animal.species, value: "\(livestock.species.displayName)")
                        DetailRow(title: L.Animal.breed, value: livestock.breed ?? L.unknown)
                        DetailRow(title: L.Animal.gender, value: livestock.sex.displayName)
                        DetailRow(title: L.Animal.birthDate, value: formatDate(livestock.birthDate))
                        DetailRow(title: L.Animal.age, value: livestock.age)
                    }
                    
                    // 状态信息
                    DetailSection(title: L.Detail.statusInfo) {
                        DetailRow(title: L.Animal.status, value: livestock.status.displayName)
                    }
                    
                    // 备注信息
                    if let notes = livestock.notes, !notes.isEmpty {
                        DetailSection(title: L.Detail.notes) {
                            Text(notes)
                                .font(.system(size: 16))
                                .foregroundColor(.black)
                                .frame(maxWidth: .infinity, alignment: .leading)
                                .padding(12)
                                .background(Color.white)
                                .cornerRadius(12)
                        }
                    }
                }
                .padding(.horizontal, 16)
                
                Spacer(minLength: 100)
            }
        }
        .background(Color(hex: "#F8F8F8"))
        .navigationBarTitleDisplayMode(.inline)
        .toolbar {
            ToolbarItem(placement: .navigationBarTrailing) {
                Menu {
                    Button(action: { showingEditSheet = true }) {
                        Label(L.edit, systemImage: "pencil")
                    }
                    
                    Button(role: .destructive, action: { showingDeleteAlert = true }) {
                        Label(L.delete, systemImage: "trash")
                    }
                } label: {
                    Image(systemName: "ellipsis.circle")
                        .foregroundColor(.black)
                }
            }
        }
        .sheet(isPresented: $showingEditSheet) {
            NavigationView {
                AddAnimalView(animal: livestock)
                    .environmentObject(dataManager)
            }
            .navigationViewStyle(.stack)
        }
        .sheet(isPresented: $showingFullImage) {
            FullImageView(imageName: livestock.photoPath, species: livestock.species)
                .environmentObject(dataManager)
        }
        .alert(L.Detail.deleteConfirm, isPresented: $showingDeleteAlert) {
            Button(L.cancel, role: .cancel) { }
            Button(L.delete, role: .destructive) {
                dataManager.deleteAnimal(livestock)
                dismiss()
            }
        } message: {
            Text(L.Detail.deleteMessage)
        }
        .onAppear {
            print("AnimalDetailView出现 - ID: \(livestock.id), 物种: \(livestock.species.displayName), 标识: \(livestock.visualId)")
            if dataManager.livestock.isEmpty {
                print("警告: dataManager.livestock为空")
            } else {
                print("dataManager中有\(dataManager.livestock.count)个动物")
            }
            print("详情页环境对象DataManager: \(type(of: dataManager))")
            
            // 检查是否能从dataManager中找到当前动物
            if dataManager.livestock.contains(where: { $0.id == livestock.id }) {
                print("在dataManager中找到了当前动物")
            } else {
                print("警告: 在dataManager中未找到当前动物!")
            }
        }
        .onDisappear {
            print("AnimalDetailView消失 - ID: \(livestock.id)")
        }
    }
    
    private func formatDate(_ date: Date?) -> String {
        guard let date = date else { return L.unknown }
        let formatter = DateFormatter()
        
        // 根据当前语言设置日期格式
        let isEnglish = Locale.current.language.languageCode?.identifier == "en"
        formatter.dateFormat = isEnglish ? "yyyy/MM/dd" : "yyyy年MM月dd日"
        
        return formatter.string(from: date)
    }
    
    private func statusColor(_ status: AnimalStatus) -> Color {
        switch status {
        case .active:
            return Color.black
        case .forSale:
            return Color.black.opacity(0.7)
        case .sold:
            return Color.black.opacity(0.5)
        case .deceased:
            return Color.black.opacity(0.6)
        case .other:
            return Color.black.opacity(0.5)
        }
    }
    
    // 为每种动物选择对应的自定义图标
    private func iconForSpecies(_ species: Species) -> String {
        switch species {
        case .cattle:
            return "cow" // 牛图标
        case .sheep:
            return "sheep" // 羊图标
        case .pig:
            return "pig" // 猪图标
        case .horse:
            return "horse" // 马图标
        case .chicken:
            return "orpington-chicken" // 鸡图标
        case .duck:
            return "duck" // 鸭图标
        case .goose:
            return "goose" // 鹅图标
        case .other:
            return "rabbit" // 其他动物图标（使用兔子作为默认）
        }
    }
}

// MARK: - 全屏图片查看视图
struct FullImageView: View {
    let imageName: String?
    let species: Species
    @Environment(\.dismiss) private var dismiss
    @EnvironmentObject var dataManager: AnimalDataManager
    
    var body: some View {
        ZStack {
            Color.black.edgesIgnoringSafeArea(.all)
            
            if let imageName = imageName, let image = dataManager.loadImage(fileName: imageName) {
                Image(uiImage: image)
                    .resizable()
                    .aspectRatio(contentMode: .fit)
                    .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                // 使用与物种相匹配的图标
                Image(iconForSpecies(species))
                    .resizable()
                    .scaledToFit()
                    .frame(width: 120, height: 120)
                    .foregroundColor(.white)
            }
            
            VStack {
                HStack {
                    Spacer()
                    Button(action: { dismiss() }) {
                        Image(systemName: "xmark.circle.fill")
                            .font(.system(size: 30))
                            .foregroundColor(.white)
                            .padding()
                    }
                }
                Spacer()
            }
        }
    }
    
    // 为每种动物选择对应的自定义图标
    private func iconForSpecies(_ species: Species) -> String {
        switch species {
        case .cattle:
            return "cow"
        case .sheep:
            return "sheep"
        case .pig:
            return "pig"
        case .horse:
            return "horse"
        case .chicken:
            return "orpington-chicken"
        case .duck:
            return "duck"
        case .goose:
            return "goose"
        case .other:
            return "rabbit"
        }
    }
}

// MARK: - 基本信息卡片
struct BasicInfoCard: View {
    let livestock: Animal
    let onImageTap: () -> Void
    @EnvironmentObject var dataManager: AnimalDataManager
    
    var body: some View {
        VStack(spacing: 16) {
            HStack(spacing: 16) {
                // 缩略图
                ZStack {
                    if let imageName = livestock.photoPath, let image = dataManager.loadImage(fileName: imageName) {
                        Image(uiImage: image)
                            .resizable()
                            .aspectRatio(contentMode: .fill)
                            .frame(width: 80, height: 80)
                            .clipShape(RoundedRectangle(cornerRadius: 10))
                            .onTapGesture {
                                onImageTap()
                            }
                    } else {
                        RoundedRectangle(cornerRadius: 10)
                            .fill(Color.black.opacity(0.05))
                            .frame(width: 80, height: 80)
                            .overlay(
                                Image(iconForSpecies(livestock.species))
                                    .resizable()
                                    .scaledToFit()
                                    .frame(width: 40, height: 40)
                                    .foregroundColor(Color.black.opacity(0.5))
                            )
                    }
                }
                
                // 基本信息
                VStack(alignment: .leading, spacing: 8) {
                    // 标识号
                    Text(livestock.visualId)
                        .font(.system(size: 20, weight: .bold))
                        .foregroundColor(.black)
                    
                    // 品种和性别
                    HStack(spacing: 8) {
                        Text(livestock.breed ?? L.unknown)
                            .foregroundColor(Color.black.opacity(0.7))
                        
                        Text("·")
                            .foregroundColor(Color.black.opacity(0.4))
                        
                        Text(livestock.sex.displayName)
                            .foregroundColor(Color.black.opacity(0.7))
                    }
                    .font(.system(size: 14))
                    
                    // 状态标签
                    HStack(spacing: 6) {
                        Circle()
                            .fill(statusColor(livestock.status))
                            .frame(width: 6, height: 6)
                        
                        Text(livestock.status.displayName)
                            .font(.system(size: 12, weight: .medium))
                            .foregroundColor(Color.black.opacity(0.7))
                    }
                    .padding(.horizontal, 8)
                    .padding(.vertical, 4)
                    .background(Color.black.opacity(0.05))
                    .cornerRadius(12)
                }
                
                Spacer()
            }
        }
        .padding(20)
        .background(Color.white)
        .cornerRadius(12)
        .overlay(
            RoundedRectangle(cornerRadius: 12)
                .stroke(Color.black.opacity(0.08), lineWidth: 1)
        )
    }
    
    private func statusColor(_ status: AnimalStatus) -> Color {
        switch status {
        case .active:
            return Color.black
        case .forSale:
            return Color.black.opacity(0.7)
        case .sold:
            return Color.black.opacity(0.5)
        case .deceased:
            return Color.black.opacity(0.6)
        case .other:
            return Color.black.opacity(0.5)
        }
    }
    
    // 为每种动物选择对应的自定义图标
    private func iconForSpecies(_ species: Species) -> String {
        switch species {
        case .cattle:
            return "cow" // 牛图标
        case .sheep:
            return "sheep" // 羊图标
        case .pig:
            return "pig" // 猪图标
        case .horse:
            return "horse" // 马图标
        case .chicken:
            return "orpington-chicken" // 鸡图标
        case .duck:
            return "duck" // 鸭图标
        case .goose:
            return "goose" // 鹅图标
        case .other:
            return "rabbit" // 其他动物图标（使用兔子作为默认）
        }
    }
}

// MARK: - 详情区块
struct DetailSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            Text(title)
                .font(.system(size: 16, weight: .semibold))
                .foregroundColor(Color.black)
            
            content
                .padding(16)
                .background(Color.white)
                .cornerRadius(12)
                .overlay(
                    RoundedRectangle(cornerRadius: 12)
                        .stroke(Color.black.opacity(0.08), lineWidth: 1)
                )
        }
    }
}

// MARK: - 详情行
struct DetailRow: View {
    let title: String
    let value: String
    
    var body: some View {
        HStack {
            Text(title)
                .font(.system(size: 16))
                .foregroundColor(Color.black.opacity(0.6))
            
            Spacer()
            
            Text(value)
                .font(.system(size: 16))
                .foregroundColor(.black)
        }
    }
}

#Preview {
    NavigationView {
        AnimalDetailView(livestock: Animal(
            visualId: "Cattle001",
            species: .cattle,
            breed: "Angus",
            sex: .male,
            birthDate: Date(),
            source: .selfBred,
            status: .active,
            notes: "This is a very healthy young calf with good growth."
        ))
        .environmentObject(AnimalDataManager())
    }
} 