import SwiftUI

struct SearchView: View {
    @Environment(\.dismiss) private var dismiss
    @State private var searchText = ""
    @State private var showClearButton = false
    @FocusState private var isSearchFocused: Bool
    @EnvironmentObject var dataManager: AnimalDataManager
    @State private var selectedAnimal: Animal? = nil
    
    // 热门搜索关键词
    var hotSearches: [String] {
        // 根据当前语言返回不同的热门搜索关键词
        let currentLang = Locale.current.languageCode ?? "en"
        if currentLang == "zh" {
            return ["安格斯", "牛", "存栏"]
        } else if currentLang == "nl" {
            return ["Angus", "Rundvee", "Actief"]
        } else {
            return ["Angus", "Cattle", "Active"]
        }
    }
    
    // 快捷标签
    var quickTags: [String] {
        // 根据当前语言返回不同的快捷标签
        let currentLang = Locale.current.languageCode ?? "en"
        if currentLang == "zh" {
            return ["牛", "羊", "猪", "存栏", "待售"]
        } else if currentLang == "nl" {
            return ["Rundvee", "Schaap", "Varken", "Actief", "Te koop"]
        } else {
            return ["Cattle", "Sheep", "Pig", "Active", "For Sale"]
        }
    }
    
    // 搜索历史
    @State private var searchHistory: [String] = []
    
    var body: some View {
        NavigationView {
            ZStack {
                // 背景色
                Color(UIColor.systemGroupedBackground)
                    .ignoresSafeArea()
                
                VStack(spacing: 0) {
                    // 搜索栏
                    SearchBar(
                        searchText: $searchText,
                        showClearButton: $showClearButton,
                        isSearchFocused: $isSearchFocused,
                        onCancel: { dismiss() }
                    )
                    .padding(.horizontal, 16)
                    .padding(.vertical, 8)
                    
                    if searchText.isEmpty {
                        // 热门搜索和快捷标签
                        ScrollView {
                            VStack(alignment: .leading, spacing: 24) {
                                // 搜索历史
                                if !searchHistory.isEmpty {
                                    VStack(alignment: .leading, spacing: 16) {
                                        HStack {
                                            Text(L.Search.recentSearches)
                                                .font(.system(size: 16, weight: .medium))
                                                .foregroundColor(.primary)
                                            
                                            Spacer()
                                            
                                            Button(L.Search.clearHistory) {
                                                searchHistory.removeAll()
                                            }
                                            .font(.system(size: 12))
                                            .foregroundColor(.gray)
                                        }
                                        
                                        VStack(alignment: .leading, spacing: 20) {
                                            ForEach(searchHistory.prefix(5), id: \.self) { keyword in
                                                Button(action: {
                                                    searchText = keyword
                                                }) {
                                                    HStack(spacing: 12) {
                                                        Image(systemName: "clock")
                                                            .font(.system(size: 16))
                                                            .foregroundColor(.gray)
                                                        
                                                        Text(keyword)
                                                            .font(.system(size: 16))
                                                            .foregroundColor(.primary)
                                                        
                                                        Spacer()
                                                    }
                                                }
                                            }
                                        }
                                    }
                                    .padding(.top, 8)
                                }
                                
                                // 热门搜索
                                VStack(alignment: .leading, spacing: 16) {
                                    Text(L.Search.hotSearches)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.primary)
                                    
                                    VStack(alignment: .leading, spacing: 20) {
                                        ForEach(hotSearches, id: \.self) { keyword in
                                            Button(action: {
                                                searchText = keyword
                                                addToSearchHistory(keyword)
                                            }) {
                                                HStack(spacing: 12) {
                                                    Image(systemName: "magnifyingglass")
                                                        .font(.system(size: 16))
                                                        .foregroundColor(.gray)
                                                    
                                                    Text(keyword)
                                                        .font(.system(size: 16))
                                                        .foregroundColor(.primary)
                                                    
                                                    Spacer()
                                                }
                                            }
                                        }
                                    }
                                }
                                .padding(.top, 8)
                                
                                // 快捷标签
                                VStack(alignment: .leading, spacing: 16) {
                                    Text(L.Search.quickSearch)
                                        .font(.system(size: 16, weight: .medium))
                                        .foregroundColor(.primary)
                                    
                                    TagCloudView(tags: quickTags) { tag in
                                        QuickTagButton(title: tag) {
                                            searchText = tag
                                            addToSearchHistory(tag)
                                        }
                                    }
                                }
                            }
                            .padding(.horizontal, 16)
                        }
                    } else {
                        // 搜索结果
                        SearchResultsView(
                            searchText: searchText,
                            animals: searchResults,
                            onSelect: { animal in
                                selectedAnimal = animal
                            }
                        )
                    }
                }
            }
            .navigationBarHidden(true)
            .onChange(of: searchText) { newValue in
                if !newValue.isEmpty {
                    performSearch()
                }
            }
            .onSubmit(of: .search) {
                if !searchText.isEmpty {
                    addToSearchHistory(searchText)
                    performSearch()
                }
            }
            .sheet(item: $selectedAnimal) { animal in
                NavigationView {
                    AnimalDetailView(livestock: animal)
                        .environmentObject(dataManager)
                }
                .navigationViewStyle(.stack)
            }
            .onAppear {
                loadSearchHistory()
            }
        }
        .navigationViewStyle(.stack)
    }
    
    // 搜索结果
    @State private var searchResults: [Animal] = []
    
    // 执行搜索
    private func performSearch() {
        searchResults = dataManager.livestock.filter { animal in
            let searchTerms = searchText.lowercased().split(separator: " ")
            
            return searchTerms.allSatisfy { term in
                let termStr = String(term)
                
                // 按ID搜索
                let idMatch = animal.visualId.lowercased().contains(termStr)
                
                // 按品种搜索
                let breedMatch = animal.breed?.lowercased().contains(termStr) ?? false
                
                // 按物种搜索（中文名称）
                let speciesMatch = animal.species.displayName.lowercased().contains(termStr)
                
                // 按状态搜索
                let statusMatch = animal.status.displayName.lowercased().contains(termStr)
                
                return idMatch || breedMatch || speciesMatch || statusMatch
            }
        }
    }
    
    // 添加到搜索历史
    private func addToSearchHistory(_ term: String) {
        // 如果已经存在，先移除
        if let index = searchHistory.firstIndex(of: term) {
            searchHistory.remove(at: index)
        }
        
        // 添加到最前面
        searchHistory.insert(term, at: 0)
        
        // 限制历史记录数量
        if searchHistory.count > 10 {
            searchHistory.removeLast()
        }
        
        // 保存历史记录
        saveSearchHistory()
    }
    
    // 保存搜索历史
    private func saveSearchHistory() {
        if let encoded = try? JSONEncoder().encode(searchHistory) {
            UserDefaults.standard.set(encoded, forKey: "search_history")
        }
    }
    
    // 加载搜索历史
    private func loadSearchHistory() {
        if let savedHistory = UserDefaults.standard.data(forKey: "search_history") {
            if let decoded = try? JSONDecoder().decode([String].self, from: savedHistory) {
                searchHistory = decoded
            }
        }
    }
}

// MARK: - 搜索栏
struct SearchBar: View {
    @Binding var searchText: String
    @Binding var showClearButton: Bool
    @FocusState.Binding var isSearchFocused: Bool
    let onCancel: () -> Void
    
    var body: some View {
        HStack(spacing: 12) {
            HStack(spacing: 8) {
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
                
                TextField(L.Search.placeholder, text: $searchText)
                    .focused($isSearchFocused)
                    .font(.system(size: 16))
                    .submitLabel(.search)
                    .onChange(of: searchText) { newValue in
                        showClearButton = !newValue.isEmpty
                    }
                
                if showClearButton {
                    Button(action: {
                        searchText = ""
                        showClearButton = false
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.gray)
                            .font(.system(size: 16))
                    }
                }
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 8)
            .background(Color.white)
            .cornerRadius(8)
            
            Button(L.cancel, action: onCancel)
                .font(.system(size: 16))
                .foregroundColor(.gray)
        }
    }
}

// MARK: - 标签云视图
struct TagCloudView<Content: View>: View {
    let tags: [String]
    let content: (String) -> Content
    
    init(tags: [String], @ViewBuilder content: @escaping (String) -> Content) {
        self.tags = tags
        self.content = content
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            var width = CGFloat.zero
            var height = CGFloat.zero
            
            GeometryReader { geometry in
                ZStack(alignment: .topLeading) {
                    ForEach(tags, id: \.self) { tag in
                        content(tag)
                            .alignmentGuide(.leading) { dimension in
                                if abs(width - dimension.width) > geometry.size.width {
                                    width = 0
                                    height -= dimension.height + 8
                                }
                                let result = width
                                if tag == tags.last {
                                    width = 0
                                } else {
                                    width -= dimension.width + 8
                                }
                                return result
                            }
                            .alignmentGuide(.top) { _ in
                                let result = height
                                if tag == tags.last {
                                    height = 0
                                }
                                return result
                            }
                    }
                }
            }
            // 预估高度
            .frame(height: CGFloat(tags.count / 3 + 1) * 40)
        }
    }
}

// MARK: - 快捷标签按钮
struct QuickTagButton: View {
    let title: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            Text(title)
                .font(.system(size: 14))
                .foregroundColor(.primary)
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color.white)
                .cornerRadius(16)
                .overlay(
                    RoundedRectangle(cornerRadius: 16)
                        .stroke(Color.gray.opacity(0.2), lineWidth: 1)
                )
        }
    }
}

// MARK: - 搜索结果视图
struct SearchResultsView: View {
    let searchText: String
    let animals: [Animal]
    let onSelect: (Animal) -> Void
    
    var body: some View {
        if animals.isEmpty {
            // 无结果状态
            VStack(spacing: 16) {
                Spacer()
                
                Image(systemName: "magnifyingglass")
                    .font(.system(size: 40))
                    .foregroundColor(.gray)
                
                Text(L.Search.noResults)
                    .font(.system(size: 16))
                    .foregroundColor(.gray)
                
                Spacer()
            }
        } else {
            // 显示搜索结果列表
            ScrollView {
                LazyVStack(spacing: 12) {
                    ForEach(animals) { animal in
                        AnimalSearchResultRow(animal: animal)
                            .onTapGesture {
                                onSelect(animal)
                            }
                    }
                }
                .padding(.horizontal, 16)
                .padding(.top, 8)
                .padding(.bottom, 16)
            }
        }
    }
}

// MARK: - 动物搜索结果行
struct AnimalSearchResultRow: View {
    let animal: Animal
    
    var body: some View {
        HStack(spacing: 12) {
            // 动物图标
            AnimalIconView(species: animal.species)
            
            // 牲畜信息
            VStack(alignment: .leading, spacing: 4) {
                HStack {
                    Text(animal.visualId)
                        .font(.system(size: 16, weight: .semibold))
                        .foregroundColor(.primary)
                    
                    Spacer()
                    
                    StatusBadge(status: animal.status)
                }
                
                Text("\(L.Animal.species): \(animal.species.displayName) | \(L.Animal.breed): \(animal.breed ?? L.unknown)")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
                
                Text("\(L.Animal.gender): \(animal.sex.displayName) | \(L.Animal.age): \(animal.age)")
                    .font(.system(size: 13))
                    .foregroundColor(.secondary)
                    .lineLimit(1)
            }
            
            // 箭头图标
            Image(systemName: "chevron.right")
                .font(.system(size: 14, weight: .medium))
                .foregroundColor(.gray)
        }
        .padding(16)
        .background(Color.white)
        .cornerRadius(16)
        .shadow(color: Color.black.opacity(0.05), radius: 8, x: 0, y: 2)
    }
}

#Preview {
    SearchView()
        .environmentObject(AnimalDataManager())
}
